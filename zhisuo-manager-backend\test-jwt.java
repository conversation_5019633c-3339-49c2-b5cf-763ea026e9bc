import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class TestJWT {
    public static void main(String[] args) {
        String secret = "zhisuo-manager-secret-key-2025-abcdef1234567890abcdef1234567890abcdef1234567890";
        
        System.out.println("Secret length: " + secret.length() + " characters");
        System.out.println("Secret length in bits: " + (secret.length() * 8) + " bits");
        
        try {
            // Test creating a JWT token with HS512
            SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
            
            Map<String, Object> claims = new HashMap<>();
            claims.put("username", "admin");
            claims.put("userId", "admin001");
            
            String token = Jwts.builder()
                    .setClaims(claims)
                    .setSubject("admin")
                    .setIssuedAt(new Date())
                    .setExpiration(new Date(System.currentTimeMillis() + 86400000))
                    .signWith(key, SignatureAlgorithm.HS512)
                    .compact();
            
            System.out.println("JWT Token created successfully!");
            System.out.println("Token: " + token);
            
        } catch (Exception e) {
            System.err.println("Error creating JWT token: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
