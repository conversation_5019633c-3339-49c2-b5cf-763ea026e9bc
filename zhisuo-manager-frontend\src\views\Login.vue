<template>
  <div class="login-container">
    <!-- 页面加载动画 -->
    <transition name="fade">
      <div v-if="pageLoading" class="page-loading">
        <div class="loading-spinner">
          <el-icon class="loading-icon-large"><Loading /></el-icon>
          <p class="loading-text">{{ t?.loading || (currentLanguage === 'en' ? 'Loading' : '加载中') }}</p>
        </div>
      </div>
    </transition>

    <!-- 主要内容区域 -->
    <transition name="slide-up">
      <div v-if="!pageLoading" class="login-main">
        <!-- 左侧品牌区域 -->
        <div class="brand-section">
          <div class="brand-content">
            <!-- Logo图标 -->
            <div class="logo-container">
              <div class="logo-hexagon">
                <div class="hexagon-inner">
                  <img src="/logo.png" alt="智索管理系统" class="logo-image" />
                </div>
              </div>
            </div>

            <!-- 系统标题 -->
            <h1 class="brand-title">智索管理系统</h1>
            <p class="brand-subtitle">智能化 · 简化管理 · 数据驱动</p>
          </div>

          <!-- 装饰元素 -->
          <div class="decoration-elements">
            <div class="decoration-dot dot-1"></div>
            <div class="decoration-dot dot-2"></div>
            <div class="decoration-dot dot-3"></div>
            <div class="decoration-line line-1"></div>
            <div class="decoration-line line-2"></div>
          </div>
        </div>

        <!-- 右侧登录区域 -->
        <div class="login-section">
          <!-- 顶部语言切换 -->
          <div class="header-actions">
            <div class="language-toggle">
              <el-dropdown @command="handleLanguageChange" trigger="click">
                <span class="language-text">
                  {{ currentLanguage === 'zh' ? '简体中文' : 'English' }}
                  <el-icon class="el-icon--right"><arrow-down /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="zh" :class="{ active: currentLanguage === 'zh' }">
                      简体中文
                    </el-dropdown-item>
                    <el-dropdown-item command="en" :class="{ active: currentLanguage === 'en' }">
                      English
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <!-- 登录表单 -->
          <div class="login-form-container">
            <h2 class="login-title">{{ t?.systemLogin || (currentLanguage === 'en' ? 'System Login' : '系统登录') }}</h2>

            <el-form
              ref="loginFormRef"
              :model="loginForm"
              :rules="loginRules"
              class="login-form"
              @keyup.enter="handleLogin"
              @submit.prevent="handleLogin"
            >
              <el-form-item prop="username">
                <el-input
                  v-model="loginForm.username"
                  :placeholder="t?.username || (currentLanguage === 'en' ? 'Username' : '用户名')"
                  size="large"
                  prefix-icon="User"
                  clearable
                  class="form-input"
                />
              </el-form-item>

              <el-form-item prop="password">
                <el-input
                  v-model="loginForm.password"
                  type="password"
                  :placeholder="t?.password || (currentLanguage === 'en' ? 'Password' : '密码')"
                  size="large"
                  prefix-icon="Lock"
                  show-password
                  clearable
                  class="form-input"
                />
              </el-form-item>

              <!-- 记住密码和忘记密码 -->
              <div class="form-options">
                <el-checkbox v-model="rememberMe" class="remember-checkbox">
                  {{ t?.rememberMe || (currentLanguage === 'en' ? 'Remember Me' : '记住密码') }}
                </el-checkbox>
                <a href="#" class="forgot-password">{{ t?.forgotPassword || (currentLanguage === 'en' ? 'Forgot Password?' : '忘记密码?') }}</a>
              </div>

              <el-form-item class="submit-item">
                <el-button
                  type="primary"
                  size="large"
                  class="login-button"
                  :loading="loading"
                  :disabled="loading"
                  @click="handleLogin"
                >
                  {{ loading ? (t?.logging || (currentLanguage === 'en' ? 'Logging in...' : '登录中...')) : (t?.login || (currentLanguage === 'en' ? 'Login' : '登录')) }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 底部版权信息 -->
          <div class="footer-copyright">
            <p>{{ t?.copyright || (currentLanguage === 'en' ? 'Copyright © 2025 Zhisuo Management System. All Rights Reserved.' : 'Copyright © 2025 智索管理系统. All Rights Reserved.') }}</p>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Loading, ArrowDown } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单数据
const loginForm = reactive({
  username: 'admin',
  password: 'admin123'
})

// 表单验证规则
const loginRules = computed(() => ({
  username: [
    { required: true, message: t.value?.usernameRequired || (currentLanguage.value === 'en' ? 'Please enter username' : '请输入用户名'), trigger: 'blur' },
    { min: 2, max: 20, message: t.value?.usernameLength || (currentLanguage.value === 'en' ? 'Username length should be 2 to 20 characters' : '用户名长度在 2 到 20 个字符'), trigger: 'blur' }
  ],
  password: [
    { required: true, message: t.value?.passwordRequired || (currentLanguage.value === 'en' ? 'Please enter password' : '请输入密码'), trigger: 'blur' },
    { min: 6, max: 20, message: t.value?.passwordLength || (currentLanguage.value === 'en' ? 'Password length should be 6 to 20 characters' : '密码长度在 6 到 20 个字符'), trigger: 'blur' }
  ]
}))

const loginFormRef = ref()
const loading = ref(false)
const pageLoading = ref(true)
const rememberMe = ref(false)

// 语言切换
const currentLanguage = ref(localStorage.getItem('language') || 'zh')

// 多语言文本
const i18nTexts = {
  zh: {
    systemLogin: '系统登录',
    username: '用户名',
    password: '密码',
    rememberMe: '记住密码',
    forgotPassword: '忘记密码?',
    login: '登录',
    logging: '登录中...',
    usernameRequired: '请输入用户名',
    passwordRequired: '请输入密码',
    usernameLength: '用户名长度在 2 到 20 个字符',
    passwordLength: '密码长度在 6 到 20 个字符',
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    copyright: 'Copyright © 2025 智索管理系统. All Rights Reserved.',
    loading: '加载中'
  },
  en: {
    systemLogin: 'System Login',
    username: 'Username',
    password: 'Password',
    rememberMe: 'Remember Me',
    forgotPassword: 'Forgot Password?',
    login: 'Login',
    logging: 'Logging in...',
    usernameRequired: 'Please enter username',
    passwordRequired: 'Please enter password',
    usernameLength: 'Username length should be 2 to 20 characters',
    passwordLength: 'Password length should be 6 to 20 characters',
    loginSuccess: 'Login successful',
    loginFailed: 'Login failed',
    copyright: 'Copyright © 2025 Zhisuo Management System. All Rights Reserved.',
    loading: 'Loading'
  }
}

// 当前语言文本
const t = computed(() => i18nTexts[currentLanguage.value])

// 页面加载完成后隐藏加载动画
onMounted(() => {
  setTimeout(() => {
    pageLoading.value = false
  }, 800)
})

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 表单验证
    await loginFormRef.value.validate()
    loading.value = true

    // 调用登录接口
    await authStore.login(loginForm)

    // 登录成功提示
    ElMessage({
      message: t.value?.loginSuccess || (currentLanguage.value === 'en' ? 'Login successful' : '登录成功'),
      type: 'success',
      duration: 2000,
      showClose: true
    })

    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
      router.push('/')
    }, 500)

  } catch (error) {
    console.error('登录失败:', error)

    // 根据错误类型显示不同的提示
    let errorMessage = '登录失败，请稍后重试'

    if (error.message) {
      if (error.message.includes('用户名') || error.message.includes('密码')) {
        errorMessage = '用户名或密码错误，请检查后重试'
      } else if (error.message.includes('网络')) {
        errorMessage = '网络连接异常，请检查网络后重试'
      } else if (error.message.includes('服务器')) {
        errorMessage = '服务器异常，请稍后重试'
      } else {
        errorMessage = error.message
      }
    }

    ElMessage({
      message: errorMessage,
      type: 'error',
      duration: 4000,
      showClose: true
    })

    // 清空密码字段
    loginForm.password = ''

  } finally {
    loading.value = false
  }
}

// 处理语言切换
const handleLanguageChange = (language) => {
  currentLanguage.value = language
  localStorage.setItem('language', language)
  ElMessage.success(language === 'zh' ? '已切换到简体中文' : 'Switched to English')
}
</script>

<style lang="scss" scoped>
// 主容器
.login-container {
  position: relative;
  height: 100vh;
  background: #f5f5f5;
  overflow: hidden;
}

// 主要内容区域
.login-main {
  display: flex;
  height: 100vh;

  // 左侧品牌区域
  .brand-section {
    flex: 0 0 40%;
    background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 50%, #9333EA 100%);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;

    .brand-content {
      text-align: center;
      color: white;
      z-index: 2;
      position: relative;

      .logo-container {
        margin-bottom: 40px;

        .logo-hexagon {
          width: 80px;
          height: 80px;
          margin: 0 auto;
          position: relative;

          .hexagon-inner {
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);

            .logo-image {
              width: 48px;
              height: 48px;
              object-fit: contain;
            }
          }
        }
      }

      .brand-title {
        font-size: 32px;
        font-weight: 600;
        margin: 0 0 16px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .brand-subtitle {
        font-size: 16px;
        opacity: 0.9;
        margin: 0;
        font-weight: 300;
        letter-spacing: 1px;
      }
    }

    // 装饰元素
    .decoration-elements {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;

      .decoration-dot {
        position: absolute;
        width: 4px;
        height: 4px;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 50%;
        animation: float 3s ease-in-out infinite;

        &.dot-1 {
          top: 20%;
          left: 15%;
          animation-delay: 0s;
        }

        &.dot-2 {
          top: 70%;
          right: 20%;
          animation-delay: 1s;
        }

        &.dot-3 {
          bottom: 30%;
          left: 25%;
          animation-delay: 2s;
        }
      }

      .decoration-line {
        position: absolute;
        background: rgba(255, 255, 255, 0.2);

        &.line-1 {
          width: 60px;
          height: 1px;
          top: 25%;
          right: 15%;
          transform: rotate(45deg);
        }

        &.line-2 {
          width: 40px;
          height: 1px;
          bottom: 25%;
          left: 20%;
          transform: rotate(-30deg);
        }
      }
    }
  }

  // 右侧登录区域
  .login-section {
    flex: 0 0 60%;
    background: white;
    display: flex;
    flex-direction: column;
    position: relative;

    .header-actions {
      position: absolute;
      top: 20px;
      right: 30px;

      .language-toggle {
        .language-text {
          color: #666;
          font-size: 14px;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 8px 12px;
          border-radius: 6px;
          transition: all 0.3s ease;

          &:hover {
            color: #8B5CF6;
            background: rgba(139, 92, 246, 0.1);
          }

          .el-icon {
            font-size: 12px;
            transition: transform 0.3s ease;
          }
        }
      }
    }

    .login-form-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 0 80px;
      max-width: 500px;
      margin: 0 auto;
      width: 100%;

      .login-title {
        font-size: 28px;
        font-weight: 600;
        color: #1a1a1a;
        margin: 0 0 40px 0;
        text-align: center;
      }
    }

    .footer-copyright {
      padding: 20px 30px;
      text-align: center;

      p {
        margin: 0;
        font-size: 12px;
        color: #999;
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-10px);
    opacity: 0.8;
  }
}

// 表单样式
.login-form {
  .el-form-item {
    margin-bottom: 24px;
  }

  :deep(.form-input) {
    .el-input__wrapper {
      border-radius: 8px;
      border: 1px solid #e1e5e9;
      box-shadow: none;
      transition: all 0.3s ease;
      background-color: #fafbfc;
      height: 48px;

      &:hover {
        border-color: #c4c9cc;
        background-color: #ffffff;
      }

      &.is-focus {
        border-color: #8B5CF6;
        box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
        background-color: #ffffff;
      }
    }

    .el-input__inner {
      height: 46px;
      font-size: 15px;
      color: #2d3748;
      background-color: transparent;

      &::placeholder {
        color: #a0aec0;
      }
    }

    .el-input__prefix {
      color: #718096;
    }

    &.is-focus .el-input__prefix {
      color: #8B5CF6;
    }
  }

  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;

    .remember-checkbox {
      :deep(.el-checkbox__label) {
        font-size: 14px;
        color: #4a5568;
      }

      :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
        background-color: #8B5CF6;
        border-color: #8B5CF6;
      }
    }

    .forgot-password {
      font-size: 14px;
      color: #8B5CF6;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .submit-item {
    margin-bottom: 0;
  }

  .login-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%);
    border: none;
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, #7C3AED 0%, #9333EA 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
    }

    &:active {
      transform: translateY(0);
    }

    &.is-loading {
      transform: none;
      cursor: not-allowed;
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .login-main {
    .login-section {
      .login-form-container {
        padding: 0 60px;
      }
    }
  }
}

@media (max-width: 768px) {
  .login-main {
    flex-direction: column;

    .brand-section {
      flex: none;
      height: 40vh;
      min-height: 300px;

      .brand-content {
        .brand-title {
          font-size: 24px;
        }

        .brand-subtitle {
          font-size: 14px;
        }
      }
    }

    .login-section {
      flex: 1;

      .login-form-container {
        padding: 0 40px;
        justify-content: flex-start;
        padding-top: 40px;

        .login-title {
          font-size: 24px;
          margin-bottom: 30px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .login-main {
    .brand-section {
      height: 35vh;
      min-height: 250px;

      .brand-content {
        .logo-container .logo-hexagon {
          width: 60px;
          height: 60px;

          .hexagon-inner .logo-icon {
            font-size: 28px;
          }
        }

        .brand-title {
          font-size: 20px;
        }

        .brand-subtitle {
          font-size: 12px;
        }
      }
    }

    .login-section {
      .login-form-container {
        padding: 0 30px;
        padding-top: 30px;

        .login-title {
          font-size: 20px;
          margin-bottom: 24px;
        }
      }

      .footer-copyright {
        padding: 15px 20px;

        p {
          font-size: 11px;
        }
      }
    }
  }
}

// 页面加载动画
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 50%, #9333EA 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box;

  .loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;

    .loading-icon-large {
      font-size: 48px;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
      display: block;
    }

    .loading-text {
      font-size: 18px;
      font-weight: 500;
      margin: 0;
      opacity: 0.9;
    }
  }
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active {
  transition: all 0.8s ease-out;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 下拉菜单样式
:deep(.el-dropdown-menu) {
  .el-dropdown-menu__item {
    &.active {
      color: #8B5CF6;
      background: rgba(139, 92, 246, 0.1);
    }
  }
}
</style>
